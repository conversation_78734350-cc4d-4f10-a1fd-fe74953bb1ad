#include "ble_gap.h"
#include "ble_server.h"
#include "ble_gatt_svc.h"

static const char *TAG = BLE_SERVER_TAG;

// 设备名称
static const char *device_name = "LinkPet-ESP";

// 广播数据
static uint8_t adv_data[] = {
    // 标志位
    0x02, 0x01, 0x06,
    // 设备名称
    0x0A, 0x09, 'L', 'i', 'n', 'k', 'P', 'e', 't', '-', 'E', 'S', 'P'
};

// 扫描响应数据
static uint8_t scan_rsp_data[] = {
    // 完整设备名称
    0x0C, 0x09, 'L', 'i', 'n', 'k', 'P', 'e', 't', '-', 'E', 'S', 'P'
};

// GAP事件处理
int ble_gap_event_handler(struct ble_gap_event *event, void *arg)
{
    int rc = 0;
    
    switch (event->type) {
    case BLE_GAP_EVENT_CONNECT:
        ESP_LOGI(TAG, "Connection %s; status=%d",
                event->connect.status == 0 ? "established" : "failed",
                event->connect.status);
        
        if (event->connect.status != 0) {
            // 连接失败，重新开始广播
            ble_gap_start_advertising();
        }
        break;
        
    case BLE_GAP_EVENT_DISCONNECT:
        ESP_LOGI(TAG, "Disconnect; reason=%d", event->disconnect.reason);
        
        // 断开连接后重新开始广播
        ble_gap_start_advertising();
        break;
        
    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI(TAG, "Advertising complete; reason=%d", event->adv_complete.reason);
        
        // 广播完成后重新开始广播
        ble_gap_start_advertising();
        break;
        
    case BLE_GAP_EVENT_SUBSCRIBE:
        ESP_LOGI(TAG, "Subscribe event; conn_handle=%d attr_handle=%d "
                "reason=%d prevn=%d curn=%d previ=%d curi=%d",
                event->subscribe.conn_handle,
                event->subscribe.attr_handle,
                event->subscribe.reason,
                event->subscribe.prev_notify,
                event->subscribe.cur_notify,
                event->subscribe.prev_indicate,
                event->subscribe.cur_indicate);
        
        // 处理订阅事件
        ble_gatt_subscribe_cb(event);
        break;
        
    default:
        break;
    }
    
    return rc;
}

int ble_gap_start_advertising(void)
{
    struct ble_gap_adv_params adv_params;
    int rc;
    
    // 设置广播参数
    memset(&adv_params, 0, sizeof(adv_params));
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;  // 可连接
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;  // 通用可发现
    adv_params.itvl_min = BLE_GAP_ADV_FAST_INTERVAL1_MIN;
    adv_params.itvl_max = BLE_GAP_ADV_FAST_INTERVAL1_MAX;
    
    // 设置广播数据
    rc = ble_gap_adv_set_data(adv_data, sizeof(adv_data));
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to set advertising data; rc=%d", rc);
        return rc;
    }
    
    // 设置扫描响应数据
    rc = ble_gap_adv_rsp_set_data(scan_rsp_data, sizeof(scan_rsp_data));
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to set scan response data; rc=%d", rc);
        return rc;
    }
    
    // 开始广播
    rc = ble_gap_adv_start(BLE_OWN_ADDR_PUBLIC, NULL, BLE_HS_FOREVER,
                          &adv_params, ble_gap_event_handler, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to start advertising; rc=%d", rc);
        return rc;
    }
    
    ESP_LOGI(TAG, "Advertising started");
    return 0;
}

int ble_gap_service_init(void)
{
    int rc;

    // 初始化GAP服务
    ble_svc_gap_init();

    // 设置设备名称
    rc = ble_svc_gap_device_name_set(device_name);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to set device name; rc=%d", rc);
        return rc;
    }

    ESP_LOGI(TAG, "GAP service initialized");
    return 0;
}
