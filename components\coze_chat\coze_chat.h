#ifndef COZE_CHAT_H
#define COZE_CHAT_H

#include "esp_err.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_coze_chat.h"
#include "coze_config.h"

#ifdef __cplusplus
extern "C" {
#endif

#define COZE_CHAT_TAG "COZE_CHAT"

// Coze配置 - 从配置文件获取
#define COZE_BOT_ID COZE_BOT_ID_cf
#define COZE_ACCESS_TOKEN COZE_ACCESS_TOKEN_cf

// 事件位定义
#define COZE_RECORDING_BIT (1 << 0)
#define COZE_PLAYING_BIT   (1 << 1)

// Coze聊天状态
typedef enum {
    COZE_CHAT_STATE_IDLE = 0,
    COZE_CHAT_STATE_RECORDING,
    COZE_CHAT_STATE_PROCESSING,
    COZE_CHAT_STATE_PLAYING,
    COZE_CHAT_STATE_ERROR
} coze_chat_state_t;

// Coze聊天管理结构
typedef struct {
    esp_coze_chat_handle_t chat_handle;
    coze_chat_state_t state;
    EventGroupHandle_t event_group;
    TaskHandle_t audio_task_handle;
    bool initialized;
    bool connected;
} coze_chat_manager_t;

/**
 * @brief 初始化Coze聊天功能
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t coze_chat_init(void);

/**
 * @brief 反初始化Coze聊天功能
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t coze_chat_deinit(void);

/**
 * @brief 开始录音并发送到Coze
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t coze_chat_start_recording(void);

/**
 * @brief 停止录音
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t coze_chat_stop_recording(void);

/**
 * @brief 取消当前对话
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_FAIL: 失败
 */
esp_err_t coze_chat_cancel(void);

/**
 * @brief 获取当前聊天状态
 * 
 * @return coze_chat_state_t 当前状态
 */
coze_chat_state_t coze_chat_get_state(void);

/**
 * @brief 检查Coze是否已连接
 * 
 * @return true 已连接
 * @return false 未连接
 */
bool coze_chat_is_connected(void);

#ifdef __cplusplus
}
#endif

#endif // COZE_CHAT_H
