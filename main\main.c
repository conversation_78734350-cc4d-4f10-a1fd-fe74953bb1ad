
#include <stdio.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "esp_timer.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "esp_err.h"
#include "esp_log.h"

#include "lvgl.h"
#include "lv_demos.h"
#include "esp_lcd_sh8601.h"
#include "touch_bsp.h"
#include "user_config.h"
#include "i2c_bsp.h"
// #include "gif_player.h"
// #include "mmap_generate_assets.h"
#include "button_bsp.h"
#include "user_audio_bsp.h"
#include "ble_server.h"
#include "wifi_provisioning.h"
#include "nvs_flash.h"
#include "coze_chat.h"
static const char *TAG = "example";

static SemaphoreHandle_t lvgl_mux = NULL;
esp_lcd_panel_io_handle_t BrigPanelHandle = NULL; //控制背光句柄

// 设备状态和GIF控制变量
static bool device_power_on = true;  // 设备开机状态
// static bool current_gif_is_joy = true;  // 当前显示的GIF (true=joy, false=swing)
// static gif_player_handle_t current_gif_handle = NULL;  // 当前GIF播放器句柄

// Coze聊天相关变量
static bool coze_chat_initialized = false;  // Coze聊天初始化状态
static bool wifi_connected = false;         // WiFi连接状态

// WiFi配网状态回调函数
static void wifi_prov_status_callback(wifi_prov_state_t state, esp_netif_ip_info_t *ip_info);

static void example_backlight_loop_task(void *arg);
static void button_event_task(void *arg);
#if CONFIG_LV_COLOR_DEPTH == 32
#define LCD_BIT_PER_PIXEL       (24)
#elif CONFIG_LV_COLOR_DEPTH == 16
#define LCD_BIT_PER_PIXEL       (16)
#endif



#define EXAMPLE_LVGL_BUF_HEIGHT        (EXAMPLE_LCD_V_RES / 4)
#define EXAMPLE_LVGL_TICK_PERIOD_MS    2
#define EXAMPLE_LVGL_TASK_MAX_DELAY_MS 500
#define EXAMPLE_LVGL_TASK_MIN_DELAY_MS 1
#define EXAMPLE_LVGL_TASK_STACK_SIZE   (4 * 1024)
#define EXAMPLE_LVGL_TASK_PRIORITY     5


static const sh8601_lcd_init_cmd_t lcd_init_cmds[] = 
{
    {0x11, (uint8_t []){0x00}, 0, 80},   
    {0xC4, (uint8_t []){0x80}, 1, 0},
    {0x53, (uint8_t []){0x20}, 1, 1},
    {0x63, (uint8_t []){0xFF}, 1, 1},
    {0x51, (uint8_t []){0x00}, 1, 1},
    {0x29, (uint8_t []){0x00}, 0, 10},
    {0x51, (uint8_t []){0xFF}, 1, 0},
};

static bool example_notify_lvgl_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
    lv_disp_flush_ready(disp_driver);
    return false;
}
static void example_lvgl_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t) drv->user_data;
    const int offsetx1 = area->x1 + 0x06;
    const int offsetx2 = area->x2 + 0x06;
    const int offsety1 = area->y1;
    const int offsety2 = area->y2;

    // copy a buffer's content to a specific area of the display
    esp_lcd_panel_draw_bitmap(panel_handle, offsetx1, offsety1, offsetx2 + 1, offsety2 + 1, color_map);
}

void example_lvgl_rounder_cb(struct _lv_disp_drv_t *disp_drv, lv_area_t *area)
{
    uint16_t x1 = area->x1;
    uint16_t x2 = area->x2;

    uint16_t y1 = area->y1;
    uint16_t y2 = area->y2;

    // round the start of coordinate down to the nearest 2M number
    area->x1 = (x1 >> 1) << 1;
    area->y1 = (y1 >> 1) << 1;
    // round the end of coordinate up to the nearest 2N+1 number
    area->x2 = ((x2 >> 1) << 1) + 1;
    area->y2 = ((y2 >> 1) << 1) + 1;
}

static void example_lvgl_touch_cb(lv_indev_drv_t *drv, lv_indev_data_t *data)
{
    uint16_t tp_x;
    uint16_t tp_y;
    uint8_t win = touch_read_coords(&tp_x,&tp_y);
    if(win)
    {
        data->point.x = tp_x;
        data->point.y = tp_y;
        data->state = LV_INDEV_STATE_PRESSED;
    }
    else 
    {
        data->state = LV_INDEV_STATE_RELEASED;
    }
}

static void example_increase_lvgl_tick(void *arg)
{
    /* Tell LVGL how many milliseconds has elapsed */
    lv_tick_inc(EXAMPLE_LVGL_TICK_PERIOD_MS);
}
static bool example_lvgl_lock(int timeout_ms)
{
    assert(lvgl_mux && "bsp_display_start must be called first");

    const TickType_t timeout_ticks = (timeout_ms == -1) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    return xSemaphoreTake(lvgl_mux, timeout_ticks) == pdTRUE;
}

static void example_lvgl_unlock(void)
{
    assert(lvgl_mux && "bsp_display_start must be called first");
    xSemaphoreGive(lvgl_mux);
}

static void example_lvgl_port_task(void *arg)
{
    ESP_LOGI(TAG, "Starting LVGL task");
    uint32_t task_delay_ms = EXAMPLE_LVGL_TASK_MAX_DELAY_MS;
    while (1) {
        // Lock the mutex due to the LVGL APIs are not thread-safe
        if (example_lvgl_lock(-1)) {
            task_delay_ms = lv_timer_handler();
            // Release the mutex
            example_lvgl_unlock();
        }
        if (task_delay_ms > EXAMPLE_LVGL_TASK_MAX_DELAY_MS) {
            task_delay_ms = EXAMPLE_LVGL_TASK_MAX_DELAY_MS;
        } else if (task_delay_ms < EXAMPLE_LVGL_TASK_MIN_DELAY_MS) {
            task_delay_ms = EXAMPLE_LVGL_TASK_MIN_DELAY_MS;
        }
        vTaskDelay(pdMS_TO_TICKS(task_delay_ms));
    }
}

void app_main(void)
{
    static lv_disp_draw_buf_t disp_buf; // contains internal graphic buffer(s) called draw buffer(s)
    static lv_disp_drv_t disp_drv;      // contains callback functions
    esp_err_t ret;

    // 初始化NVS Flash (BLE需要)
    ESP_LOGI(TAG, "Initialize NVS Flash");
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化BLE服务器
    ESP_LOGI(TAG, "Initialize BLE server");
    ret = ble_server_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BLE server: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "BLE server initialized successfully");
        // 启动BLE服务器
        ble_server_start();
    }

    // 初始化WiFi配网
    ESP_LOGI(TAG, "Initialize WiFi provisioning");
    ret = wifi_prov_init(wifi_prov_status_callback);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi provisioning: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "WiFi provisioning initialized successfully");
    }

    i2c_master_Init();

    // 初始化音频BSP
    ESP_LOGI(TAG, "Initialize audio BSP");
    user_audio_bsp_init();

    ESP_LOGI(TAG, "Initialize SPI bus");

    spi_bus_config_t buscfg = {};
    buscfg.sclk_io_num =  EXAMPLE_PIN_NUM_LCD_PCLK;  
    buscfg.data0_io_num = EXAMPLE_PIN_NUM_LCD_DATA0;            
    buscfg.data1_io_num = EXAMPLE_PIN_NUM_LCD_DATA1;             
    buscfg.data2_io_num = EXAMPLE_PIN_NUM_LCD_DATA2;
    buscfg.data3_io_num = EXAMPLE_PIN_NUM_LCD_DATA3;
    buscfg.max_transfer_sz = (EXAMPLE_LCD_H_RES * EXAMPLE_LCD_V_RES * LCD_BIT_PER_PIXEL / 8);
    buscfg.isr_cpu_id =  ESP_INTR_CPU_AFFINITY_0;

    ESP_ERROR_CHECK(spi_bus_initialize(LCD_HOST, &buscfg, SPI_DMA_CH_AUTO));  
    ESP_LOGI(TAG, "Install panel IO");
    esp_lcd_panel_io_handle_t io_handle = NULL;
    esp_lcd_panel_io_spi_config_t io_config = {};
    io_config.cs_gpio_num = EXAMPLE_PIN_NUM_LCD_CS;              
    io_config.dc_gpio_num = -1;          
    io_config.spi_mode = 0;              
    io_config.pclk_hz = 40 * 1000 * 1000;
    io_config.trans_queue_depth = 10;    
    io_config.on_color_trans_done = example_notify_lvgl_flush_ready;  
    io_config.user_ctx = &disp_drv;         
    io_config.lcd_cmd_bits = 32;         
    io_config.lcd_param_bits = 8;        
    io_config.flags.quad_mode = true;

    sh8601_vendor_config_t vendor_config = {};
    vendor_config.flags.use_qspi_interface = 1;
    vendor_config.init_cmds = lcd_init_cmds;
    vendor_config.init_cmds_size = sizeof(lcd_init_cmds) / sizeof(lcd_init_cmds[0]);
    // Attach the LCD to the SPI bus
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_HOST, &io_config, &io_handle));
    BrigPanelHandle = io_handle;

    esp_lcd_panel_handle_t panel_handle = NULL;
    esp_lcd_panel_dev_config_t panel_config = {};
    panel_config.reset_gpio_num = EXAMPLE_PIN_NUM_LCD_RST;
    panel_config.rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB;
    panel_config.bits_per_pixel = LCD_BIT_PER_PIXEL;
    panel_config.vendor_config = &vendor_config;
    
    ESP_LOGI(TAG, "Install SH8601 panel driver");
    ESP_ERROR_CHECK(esp_lcd_new_panel_sh8601(io_handle, &panel_config, &panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_reset(panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(panel_handle));
    // user can flush pre-defined pattern to the screen before we turn on the screen or backlight
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(panel_handle, true));

    disp_touch_init(); //touch initialization

    ESP_LOGI(TAG, "Initialize LVGL library");
    lv_init();
    // alloc draw buffers used by LVGL
    // it's recommended to choose the size of the draw buffer(s) to be at least 1/10 screen sized
    lv_color_t *buf1 = (lv_color_t *)heap_caps_malloc(EXAMPLE_LCD_H_RES * 50 * sizeof(lv_color_t), MALLOC_CAP_DMA);
    assert(buf1);
    lv_color_t *buf2 = (lv_color_t *)heap_caps_malloc(EXAMPLE_LCD_H_RES * 50 * sizeof(lv_color_t), MALLOC_CAP_DMA);
    assert(buf2);
    // initialize LVGL draw buffers
    lv_disp_draw_buf_init(&disp_buf, buf1, buf2, EXAMPLE_LCD_H_RES * 50);

    ESP_LOGI(TAG, "Register display driver to LVGL");
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = EXAMPLE_LCD_H_RES;
    disp_drv.ver_res = EXAMPLE_LCD_V_RES;
    disp_drv.flush_cb = example_lvgl_flush_cb;
    disp_drv.rounder_cb = example_lvgl_rounder_cb;
    disp_drv.draw_buf = &disp_buf;
    disp_drv.user_data = panel_handle;
#ifdef EXAMPLE_Rotate_90
    disp_drv.sw_rotate = 1;
    disp_drv.rotated = LV_DISP_ROT_270;
#endif
    lv_disp_t *disp = lv_disp_drv_register(&disp_drv);

    ESP_LOGI(TAG, "Install LVGL tick timer");
    // Tick interface for LVGL (using esp_timer to generate 2ms periodic event)
    esp_timer_create_args_t lvgl_tick_timer_args = {};
    lvgl_tick_timer_args.callback = &example_increase_lvgl_tick;
    lvgl_tick_timer_args.name = "lvgl_tick";

    esp_timer_handle_t lvgl_tick_timer = NULL;
    ESP_ERROR_CHECK(esp_timer_create(&lvgl_tick_timer_args, &lvgl_tick_timer));
    ESP_ERROR_CHECK(esp_timer_start_periodic(lvgl_tick_timer, EXAMPLE_LVGL_TICK_PERIOD_MS * 1000));

    static lv_indev_drv_t indev_drv;    // Input device driver (Touch)
    lv_indev_drv_init(&indev_drv);
    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.disp = disp;
    indev_drv.read_cb = example_lvgl_touch_cb;
    lv_indev_drv_register(&indev_drv);


    lvgl_mux = xSemaphoreCreateMutex();
    assert(lvgl_mux);
    xTaskCreatePinnedToCore(example_lvgl_port_task, "LVGL", EXAMPLE_LVGL_TASK_STACK_SIZE,NULL, EXAMPLE_LVGL_TASK_PRIORITY, NULL ,0); //运行于内核_0 
    xTaskCreatePinnedToCore(example_backlight_loop_task, "example_backlight_loop_task", 4 * 1024, NULL, 2, NULL,0);
    // ESP_LOGI(TAG, "Initialize GIF player and display animated GIF");

    // Initialize the GIF player component
    // gif_player_err_t gif_err = gif_player_init();
    // if (gif_err != GIF_PLAYER_OK) {
    //     ESP_LOGE(TAG, "Failed to initialize GIF player: %d", gif_err);
    //     return;
    // }

    // Initialize button system
    ESP_LOGI(TAG, "Initialize button system");
    user_button_init();
    
    // Lock the mutex due to the LVGL APIs are not thread-safe
    if (example_lvgl_lock(-1))
    {
        // Create GIF player using memory-mapped assets
        // gif_err = gif_player_create_from_mmap(
        //     MMAP_ASSETS_JOY_GIF,
        //     lv_scr_act(),          // Parent object
        //     233,                   // X position
        //     233,                   // Y position
        //     256,                   // Zoom
        //     true,                  // Loop forever
        //     &current_gif_handle    // Save handle globally
        // );

        // if (gif_err != GIF_PLAYER_OK) {
        //     ESP_LOGE(TAG, "Failed to create GIF player from mmap: %d", gif_err);
        // } else {
        //     ESP_LOGI(TAG, "GIF player created successfully from memory-mapped assets");
        // }

        // Release the mutex
        example_lvgl_unlock();
    }

    // Create button event handling task
    ESP_LOGI(TAG, "Create button event task");
    xTaskCreatePinnedToCore(button_event_task, "button_event_task", 4 * 1024, NULL, 3, NULL, 0);
}

void setBrightnes(uint8_t brig)
{
  uint32_t lcd_cmd = 0x51;
  lcd_cmd &= 0xff;
  lcd_cmd <<= 8;
  lcd_cmd |= 0x02 << 24;
  uint8_t param = brig;
  esp_lcd_panel_io_tx_param(BrigPanelHandle, lcd_cmd, &param,1);
}

static void example_backlight_loop_task(void *arg)
{
  for(;;)
  {
#ifdef  Backlight_Testing
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(255);
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(200);
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(150);
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(100);
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(50);
    vTaskDelay(pdMS_TO_TICKS(2000));
    setBrightnes(0);
#else
    vTaskDelay(pdMS_TO_TICKS(2000));
#endif
  }
}

// 切换GIF显示
// static void switch_gif_display(void)
// {
//     if (!example_lvgl_lock(-1)) {
//         ESP_LOGE(TAG, "Failed to lock LVGL mutex for GIF switch");
//         return;
//     }

//     // 销毁当前GIF
//     if (current_gif_handle != NULL) {
//         gif_player_destroy(current_gif_handle);
//         current_gif_handle = NULL;
//     }

//     // 切换到另一个GIF
//     current_gif_is_joy = !current_gif_is_joy;
//     int asset_index = current_gif_is_joy ? MMAP_ASSETS_JOY_GIF : MMAP_ASSETS_SWING_GIF;

//     gif_player_err_t gif_err = gif_player_create_from_mmap(
//         asset_index,
//         lv_scr_act(),          // Parent object
//         233,                   // X position
//         233,                   // Y position
//         256,                   // Zoom
//         true,                  // Loop forever
//         &current_gif_handle
//     );

//     if (gif_err != GIF_PLAYER_OK) {
//         ESP_LOGE(TAG, "Failed to create GIF player: %d", gif_err);
//     } else {
//         ESP_LOGI(TAG, "Switched to %s GIF", current_gif_is_joy ? "joy" : "swing");
//     }

//     example_lvgl_unlock();
// }

// 按键事件处理任务
static void button_event_task(void *arg)
{
    EventBits_t event_bits;
    const EventBits_t button1_long_press_bit = (1 << 5);   // 按键1长按
    const EventBits_t button1_single_click_bit = (1 << 0); // 按键1单击 - 用于Coze对话
    const EventBits_t button2_long_press_bit = (1 << 12);  // 按键2长按
    const EventBits_t button2_single_click_bit = (1 << 7); // 按键2单击

    ESP_LOGI(TAG, "Button event task started");

    static bool coze_recording = false;  // Coze录音状态

    for(;;)
    {
        // 等待按键事件
        event_bits = xEventGroupWaitBits(
            key_groups,
            button1_long_press_bit | button1_single_click_bit | button2_long_press_bit | button2_single_click_bit,
            pdTRUE,  // 清除事件位
            pdFALSE, // 等待任意一个事件
            portMAX_DELAY
        );

        // 处理按键1单击 - Coze对话控制
        if (event_bits & button1_single_click_bit) {
            ESP_LOGI(TAG, "Button 1 clicked - Coze recording");
            ESP_LOGI(TAG, "device_power_on: %d, coze_chat_initialized: %d, wifi_connected: %d", device_power_on, coze_chat_initialized, wifi_connected);
            if (device_power_on && coze_chat_initialized && wifi_connected) {
                if (!coze_recording) {
                    // 开始录音
                    ESP_LOGI(TAG, "Starting Coze recording...");
                    esp_err_t ret = coze_chat_start_recording();
                    if (ret == ESP_OK) {
                        coze_recording = true;
                        ESP_LOGI(TAG, "Coze recording started");
                        // 可以在这里切换到录音GIF动画
                        // switch_to_recording_gif();
                    } else {
                        ESP_LOGE(TAG, "Failed to start Coze recording");
                    }
                } else {
                    // 停止录音
                    ESP_LOGI(TAG, "Stopping Coze recording...");
                    esp_err_t ret = coze_chat_stop_recording();
                    if (ret == ESP_OK) {
                        coze_recording = false;
                        ESP_LOGI(TAG, "Coze recording stopped");
                        // 可以在这里切换到处理GIF动画
                        // switch_to_processing_gif();
                    } else {
                        ESP_LOGE(TAG, "Failed to stop Coze recording");
                    }
                }
            } else {
                if (!device_power_on) {
                    ESP_LOGW(TAG, "Device is powered off");
                } else if (!wifi_connected) {
                    ESP_LOGW(TAG, "WiFi not connected");
                } else if (!coze_chat_initialized) {
                    ESP_LOGW(TAG, "Coze chat not initialized");
                }
            }
        }

        // 处理按键1长按 - 开机/关机
        if (event_bits & button1_long_press_bit) {
            device_power_on = !device_power_on;
            if (device_power_on) {
                ESP_LOGI(TAG, "Device POWER ON");
                setBrightnes(255);  // 开机时设置最大亮度
            } else {
                ESP_LOGI(TAG, "Device POWER OFF");
                setBrightnes(0);    // 关机时关闭背光
                // 关机时停止Coze录音
                if (coze_recording) {
                    coze_chat_cancel();
                    coze_recording = false;
                }
            }
        }

        // 处理按键2长按 - 配网模式
        if (event_bits & button2_long_press_bit) {
            ESP_LOGI(TAG, "Entering WiFi configuration mode...");
            // TODO: 实现具体的配网逻辑
            // gif_player_create_from_mmap(
            // MMAP_ASSETS_POUNCE_GIF,
            // lv_scr_act(),          // Parent object
            // 233,                   // X position
            // 233,                   // Y position
            // 256,                   // Zoom
            // true,                  // Loop forever
            // &current_gif_handle    // Save handle globally
            // );
        }

        // 处理按键2单击 - 切换GIF
        if (event_bits & button2_single_click_bit) {
            if (device_power_on) {  // 只有在开机状态下才切换GIF
                ESP_LOGI(TAG, "Button 2 clicked - GIF switching disabled");
                // switch_gif_display();
            }
        }
    }
}

// WiFi配网状态回调函数
static void wifi_prov_status_callback(wifi_prov_state_t state, esp_netif_ip_info_t *ip_info)
{
    switch (state) {
    case WIFI_PROV_STATE_IDLE:
        ESP_LOGI(TAG, "WiFi provisioning: IDLE");
        wifi_connected = false;
        break;
    case WIFI_PROV_STATE_CONNECTING:
        ESP_LOGI(TAG, "WiFi provisioning: CONNECTING");
        wifi_connected = false;
        break;
    case WIFI_PROV_STATE_CONNECTED:
        if (ip_info) {
            ESP_LOGI(TAG, "WiFi provisioning: CONNECTED, IP: " IPSTR, IP2STR(&ip_info->ip));
        } else {
            ESP_LOGI(TAG, "WiFi provisioning: CONNECTED");
        }
        wifi_connected = true;

        // WiFi连接成功后初始化Coze聊天
        if (!coze_chat_initialized) {
            ESP_LOGI(TAG, "Initializing Coze chat after WiFi connection...");
            esp_err_t ret = coze_chat_init();
            if (ret == ESP_OK) {
                coze_chat_initialized = true;
                ESP_LOGI(TAG, "Coze chat initialized successfully");
            } else {
                ESP_LOGE(TAG, "Failed to initialize Coze chat: %s", esp_err_to_name(ret));
            }
        }
        break;
    case WIFI_PROV_STATE_FAILED:
        ESP_LOGI(TAG, "WiFi provisioning: FAILED");
        wifi_connected = false;
        break;
    case WIFI_PROV_STATE_DISCONNECTED:
        ESP_LOGI(TAG, "WiFi provisioning: DISCONNECTED");
        wifi_connected = false;
        // WiFi断开时反初始化Coze聊天
        if (coze_chat_initialized) {
            ESP_LOGI(TAG, "Deinitializing Coze chat due to WiFi disconnection...");
            coze_chat_deinit();
            coze_chat_initialized = false;
        }
        break;
    default:
        ESP_LOGI(TAG, "WiFi provisioning: UNKNOWN STATE");
        break;
    }
}
