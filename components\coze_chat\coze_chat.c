#include "coze_chat.h"
#include "user_audio_bsp.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include "coze_config.h"
#include "esp_log.h"
#include "esp_err.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_coze_chat.h"
#include "esp_codec_dev.h"

static coze_chat_manager_t g_coze_manager = {0};

// 外部变量声明 - 来自audio_bsp组件
extern esp_codec_dev_handle_t playback;
extern esp_codec_dev_handle_t record;

// 音频数据回调函数 - 接收Coze返回的音频数据
static void coze_audio_data_callback(char *data, int len, void *ctx)
{
    ESP_LOGI(COZE_CHAT_TAG, "Received audio data: %d bytes", len);

    // 设置播放状态
    g_coze_manager.state = COZE_CHAT_STATE_PLAYING;
    xEventGroupSetBits(g_coze_manager.event_group, COZE_PLAYING_BIT);

    // 将音频数据发送到播放器
    if (playback && data && len > 0) {
        // 配置播放参数 - 符合Coze输出：16kHz, 16位, 单声道
        static bool playback_opened = false;
        if (!playback_opened) {
            esp_codec_dev_sample_info_t playback_fs = {
                .sample_rate = 16000,      // Coze输出16kHz
                .channel = 1,              // 单声道
                .bits_per_sample = 16,     // 16位深度
            };

            esp_err_t ret = esp_codec_dev_open(playback, &playback_fs);
            if (ret == ESP_CODEC_DEV_OK) {
                playback_opened = true;
                ESP_LOGI(COZE_CHAT_TAG, "Playback device opened successfully");
                // 设置播放音量
                esp_codec_dev_set_out_vol(playback, 60.0);
            } else {
                ESP_LOGE(COZE_CHAT_TAG, "Failed to open playback device");
                return;
            }
        }

        // 播放音频数据
        esp_err_t ret = esp_codec_dev_write(playback, (uint8_t*)data, len);
        if (ret == ESP_CODEC_DEV_OK) {
            ESP_LOGI(COZE_CHAT_TAG, "Audio data sent to playback successfully");
        } else {
            ESP_LOGE(COZE_CHAT_TAG, "Failed to write audio data to playback: %d", ret);
        }
    } else {
        ESP_LOGW(COZE_CHAT_TAG, "Playback device not available or invalid data");
    }
}

// 事件回调函数 - 处理Coze聊天事件
static void coze_event_callback(esp_coze_chat_event_t event, char *data, void *ctx)
{
    switch (event) {
        case ESP_COZE_CHAT_EVENT_CHAT_CREATE:
            ESP_LOGI(COZE_CHAT_TAG, "Chat session created");
            g_coze_manager.connected = true;
            break;

        case ESP_COZE_CHAT_EVENT_CHAT_UPDATE:
            ESP_LOGI(COZE_CHAT_TAG, "Chat session updated");
            // 收到chat.update事件表示连接已建立并配置完成
            g_coze_manager.connected = true;
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_COMPLETED:
            ESP_LOGI(COZE_CHAT_TAG, "Chat session completed");
            g_coze_manager.state = COZE_CHAT_STATE_IDLE;
            xEventGroupClearBits(g_coze_manager.event_group, COZE_PLAYING_BIT);
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_SPEECH_STARTED:
            ESP_LOGI(COZE_CHAT_TAG, "Speech output started");
            g_coze_manager.state = COZE_CHAT_STATE_PLAYING;
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_SPEECH_STOPED:
            ESP_LOGI(COZE_CHAT_TAG, "Speech output stopped");
            g_coze_manager.state = COZE_CHAT_STATE_IDLE;
            xEventGroupClearBits(g_coze_manager.event_group, COZE_PLAYING_BIT);
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_ERROR:
            ESP_LOGE(COZE_CHAT_TAG, "Chat error occurred: %s", data ? data : "Unknown error");
            g_coze_manager.state = COZE_CHAT_STATE_ERROR;
            break;
            
        case ESP_COZE_CHAT_EVENT_INPUT_AUDIO_BUFFER_COMPLETED:
            ESP_LOGI(COZE_CHAT_TAG, "Input audio buffer completed");
            g_coze_manager.state = COZE_CHAT_STATE_PROCESSING;
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_SUBTITLE_EVENT:
            ESP_LOGI(COZE_CHAT_TAG, "Subtitle: %s", data ? data : "");
            break;
            
        case ESP_COZE_CHAT_EVENT_CHAT_CUSTOMER_DATA:
            ESP_LOGI(COZE_CHAT_TAG, "Customer data: %s", data ? data : "");
            break;
            
        default:
            ESP_LOGW(COZE_CHAT_TAG, "Unknown event: %d", event);
            break;
    }
}

// 音频录音任务
static void coze_audio_recording_task(void *pvParameters)
{
    const int buffer_size = 1024;
    uint8_t *audio_buffer = malloc(buffer_size);
    if (!audio_buffer) {
        ESP_LOGE(COZE_CHAT_TAG, "Failed to allocate audio buffer");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(COZE_CHAT_TAG, "Audio recording task started");

    // 配置录音参数 - 符合Coze要求：16kHz, 16位, 单声道
    esp_codec_dev_sample_info_t record_fs = {
        .sample_rate = 16000,      // Coze要求16kHz
        .channel = 1,              // 单声道
        .bits_per_sample = 16,     // 16位深度
    };

    bool record_opened = false;

    while (1) {
        // 等待录音事件
        EventBits_t bits = xEventGroupWaitBits(
            g_coze_manager.event_group,
            COZE_RECORDING_BIT,
            pdFALSE,  // 不清除事件位
            pdFALSE,  // 等待任意一个事件
            portMAX_DELAY
        );

        if (bits & COZE_RECORDING_BIT) {
            // 如果录音设备还没打开，先打开它
            if (!record_opened && record) {
                esp_err_t ret = esp_codec_dev_open(record, &record_fs);
                if (ret == ESP_CODEC_DEV_OK) {
                    record_opened = true;
                    ESP_LOGI(COZE_CHAT_TAG, "Record device opened successfully");
                    // 设置录音增益
                    esp_codec_dev_set_in_gain(record, 35.0);
                } else {
                    ESP_LOGE(COZE_CHAT_TAG, "Failed to open record device");
                    vTaskDelay(pdMS_TO_TICKS(100));
                    continue;
                }
            }

            // 持续录音直到录音位被清除
            while (xEventGroupGetBits(g_coze_manager.event_group) & COZE_RECORDING_BIT) {
                // 从录音设备读取数据
                if (record_opened && record) {
                    esp_err_t ret = esp_codec_dev_read(record, audio_buffer, buffer_size);
                    if (ret == ESP_CODEC_DEV_OK) {
                        // 发送音频数据到Coze
                        esp_err_t send_ret = esp_coze_chat_send_audio_data(
                            g_coze_manager.chat_handle,
                            (char*)audio_buffer,
                            buffer_size
                        );

                        if (send_ret != ESP_OK) {
                            ESP_LOGE(COZE_CHAT_TAG, "Failed to send audio data: %s", esp_err_to_name(send_ret));
                        }
                    } else {
                        ESP_LOGW(COZE_CHAT_TAG, "Failed to read audio data: %d", ret);
                        vTaskDelay(pdMS_TO_TICKS(10)); // 短暂延迟后重试
                    }
                } else {
                    vTaskDelay(pdMS_TO_TICKS(10));
                }
            }

            // 录音停止，关闭录音设备
            if (record_opened && record) {
                esp_codec_dev_close(record);
                record_opened = false;
                ESP_LOGI(COZE_CHAT_TAG, "Record device closed");
            }
        }
    }
    
    free(audio_buffer);
    vTaskDelete(NULL);
}

esp_err_t coze_chat_init(void)
{
    if (g_coze_manager.initialized) {
        ESP_LOGW(COZE_CHAT_TAG, "Coze chat already initialized");
        return ESP_OK;
    }
    
    ESP_LOGI(COZE_CHAT_TAG, "Initializing Coze chat...");
    
    // 检查WiFi连接状态
    wifi_ap_record_t ap_info;
    if (esp_wifi_sta_get_ap_info(&ap_info) != ESP_OK) {
        ESP_LOGE(COZE_CHAT_TAG, "WiFi not connected. Please connect to WiFi first.");
        return ESP_FAIL;
    }
    
    // 创建事件组
    g_coze_manager.event_group = xEventGroupCreate();
    if (!g_coze_manager.event_group) {
        ESP_LOGE(COZE_CHAT_TAG, "Failed to create event group");
        return ESP_FAIL;
    }
    
    // 配置Coze聊天
    esp_coze_chat_config_t chat_config = ESP_COZE_CHAT_DEFAULT_CONFIG();
    chat_config.bot_id = COZE_BOT_ID;
    chat_config.access_token = COZE_ACCESS_TOKEN;
    chat_config.mode = ESP_COZE_CHAT_NORMAL_MODE;  // 使用按键触发模式
    chat_config.uplink_audio_type = ESP_COZE_CHAT_AUDIO_TYPE_PCM;    // 上行音频格式
    chat_config.downlink_audio_type = ESP_COZE_CHAT_AUDIO_TYPE_PCM;  // 下行音频格式
    chat_config.audio_callback = coze_audio_data_callback;
    chat_config.event_callback = coze_event_callback;
    chat_config.enable_subtitle = true;  // 启用字幕

    // 调整任务配置以适应ESP32-C6单核处理器
    chat_config.pull_task_stack_size = 2048;  // 进一步减少栈大小
    chat_config.push_task_stack_size = 2048;  // 进一步减少栈大小
    chat_config.pull_task_core = 0;  // ESP32-C6只有核心0
    chat_config.push_task_core = 0;  // ESP32-C6只有核心0
    chat_config.pull_task_caps = MALLOC_CAP_8BIT;  // 使用内部RAM
    chat_config.push_task_caps = MALLOC_CAP_8BIT;  // 使用内部RAM
    chat_config.websocket_buffer_size = 4096;  // 进一步减少缓冲区大小
    
    // 初始化Coze聊天
    esp_err_t ret = esp_coze_chat_init(&chat_config, &g_coze_manager.chat_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(COZE_CHAT_TAG, "Failed to initialize Coze chat: %s", esp_err_to_name(ret));
        vEventGroupDelete(g_coze_manager.event_group);
        return ret;
    }
    
    // 启动Coze聊天会话
    ret = esp_coze_chat_start(g_coze_manager.chat_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(COZE_CHAT_TAG, "Failed to start Coze chat: %s", esp_err_to_name(ret));
        esp_coze_chat_deinit(g_coze_manager.chat_handle);
        vEventGroupDelete(g_coze_manager.event_group);
        return ret;
    }

    // WebSocket连接成功，设置为已连接状态
    // 实际的聊天配置会在收到CHAT_UPDATE事件时完成
    g_coze_manager.connected = true;
    
    // 创建音频录音任务
    BaseType_t task_ret = xTaskCreatePinnedToCore(
        coze_audio_recording_task,
        "coze_audio_task",
        2048,  // 减少栈大小
        NULL,
        10,
        &g_coze_manager.audio_task_handle,
        0  // ESP32-C6只有核心0
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(COZE_CHAT_TAG, "Failed to create audio recording task");
        esp_coze_chat_stop(g_coze_manager.chat_handle);
        esp_coze_chat_deinit(g_coze_manager.chat_handle);
        vEventGroupDelete(g_coze_manager.event_group);
        return ESP_FAIL;
    }
    
    g_coze_manager.state = COZE_CHAT_STATE_IDLE;
    g_coze_manager.initialized = true;
    // connected状态已在esp_coze_chat_start成功后设置为true，不要覆盖
    
    ESP_LOGI(COZE_CHAT_TAG, "Coze chat initialized successfully");
    return ESP_OK;
}

esp_err_t coze_chat_deinit(void)
{
    if (!g_coze_manager.initialized) {
        ESP_LOGW(COZE_CHAT_TAG, "Coze chat not initialized");
        return ESP_OK;
    }

    ESP_LOGI(COZE_CHAT_TAG, "Deinitializing Coze chat...");

    // 停止录音
    coze_chat_stop_recording();

    // 删除音频任务
    if (g_coze_manager.audio_task_handle) {
        vTaskDelete(g_coze_manager.audio_task_handle);
        g_coze_manager.audio_task_handle = NULL;
    }

    // 停止并反初始化Coze聊天
    if (g_coze_manager.chat_handle) {
        esp_coze_chat_stop(g_coze_manager.chat_handle);
        esp_coze_chat_deinit(g_coze_manager.chat_handle);
        g_coze_manager.chat_handle = NULL;
    }

    // 删除事件组
    if (g_coze_manager.event_group) {
        vEventGroupDelete(g_coze_manager.event_group);
        g_coze_manager.event_group = NULL;
    }

    g_coze_manager.initialized = false;
    g_coze_manager.connected = false;
    g_coze_manager.state = COZE_CHAT_STATE_IDLE;

    ESP_LOGI(COZE_CHAT_TAG, "Coze chat deinitialized");
    return ESP_OK;
}

esp_err_t coze_chat_start_recording(void)
{
    if (!g_coze_manager.initialized) {
        ESP_LOGE(COZE_CHAT_TAG, "Coze chat not initialized");
        return ESP_FAIL;
    }

    if (!g_coze_manager.connected) {
        ESP_LOGE(COZE_CHAT_TAG, "Coze chat not connected");
        return ESP_FAIL;
    }

    ESP_LOGI(COZE_CHAT_TAG, "Starting recording...");

    // 取消之前的对话（如果有）
    esp_coze_chat_send_audio_cancel(g_coze_manager.chat_handle);

    // 设置录音状态
    g_coze_manager.state = COZE_CHAT_STATE_RECORDING;
    xEventGroupSetBits(g_coze_manager.event_group, COZE_RECORDING_BIT);

    return ESP_OK;
}

esp_err_t coze_chat_stop_recording(void)
{
    if (!g_coze_manager.initialized) {
        ESP_LOGE(COZE_CHAT_TAG, "Coze chat not initialized");
        return ESP_FAIL;
    }

    ESP_LOGI(COZE_CHAT_TAG, "Stopping recording...");

    // 清除录音状态
    xEventGroupClearBits(g_coze_manager.event_group, COZE_RECORDING_BIT);

    // 发送音频完成信号
    if (g_coze_manager.chat_handle) {
        esp_err_t ret = esp_coze_chat_send_audio_complete(g_coze_manager.chat_handle);
        if (ret != ESP_OK) {
            ESP_LOGE(COZE_CHAT_TAG, "Failed to send audio complete: %s", esp_err_to_name(ret));
            return ret;
        }
    }

    g_coze_manager.state = COZE_CHAT_STATE_PROCESSING;
    return ESP_OK;
}

esp_err_t coze_chat_cancel(void)
{
    if (!g_coze_manager.initialized) {
        ESP_LOGE(COZE_CHAT_TAG, "Coze chat not initialized");
        return ESP_FAIL;
    }

    ESP_LOGI(COZE_CHAT_TAG, "Cancelling chat...");

    // 停止录音
    xEventGroupClearBits(g_coze_manager.event_group, COZE_RECORDING_BIT);

    // 取消对话
    if (g_coze_manager.chat_handle) {
        esp_err_t ret = esp_coze_chat_send_audio_cancel(g_coze_manager.chat_handle);
        if (ret != ESP_OK) {
            ESP_LOGE(COZE_CHAT_TAG, "Failed to cancel chat: %s", esp_err_to_name(ret));
            return ret;
        }
    }

    g_coze_manager.state = COZE_CHAT_STATE_IDLE;
    return ESP_OK;
}

coze_chat_state_t coze_chat_get_state(void)
{
    return g_coze_manager.state;
}

bool coze_chat_is_connected(void)
{
    return g_coze_manager.connected;
}
