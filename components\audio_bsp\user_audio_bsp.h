#ifndef USER_AUDIO_BSP_H
#define USER_AUDIO_BSP_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "esp_codec_dev.h"

#ifdef __cplusplus
extern "C" {
#endif

// 音频播放状态
typedef enum {
    AUDIO_PLAYBACK_STATE_IDLE = 0,
    AUDIO_PLAYBACK_STATE_PLAYING,
    AUDIO_PLAYBACK_STATE_PAUSED,
    AUDIO_PLAYBACK_STATE_ERROR
} audio_playback_state_t;

// 音频格式配置
typedef struct {
    uint32_t sample_rate;     // 采样率
    uint8_t  channels;        // 声道数
    uint8_t  bits_per_sample; // 位深度
} audio_format_t;

// 基础音频BSP函数
void user_audio_bsp_init(void);
void i2s_music(void *args);
void i2s_echo(void *arg);
void audio_playback_set_vol(uint8_t vol);

// 新增的音频播放功能
esp_err_t audio_playback_init(void);
esp_err_t audio_playback_deinit(void);
esp_err_t audio_playback_start(const audio_format_t *format);
esp_err_t audio_playback_stop(void);
esp_err_t audio_playback_write_data(const uint8_t *data, size_t len);
esp_err_t audio_playback_set_volume(float volume);
audio_playback_state_t audio_playback_get_state(void);

// 获取音频设备句柄（供其他组件使用）
esp_codec_dev_handle_t audio_get_playback_handle(void);
esp_codec_dev_handle_t audio_get_record_handle(void);

// 测试函数
esp_err_t audio_playback_test_tone(uint32_t frequency, uint32_t duration_ms);

#ifdef __cplusplus
}
#endif

#endif // !USER_AUDIO_BSP_H
