# Coze对话集成使用说明

本项目已集成esp-coze库，实现了与Coze平台的语音对话功能。

## 功能特性

- 支持语音录音并发送到Coze平台
- 接收Coze返回的语音回复
- 按键控制录音开始/停止
- 自动WiFi连接后初始化Coze聊天
- 实时状态监控和错误处理

## 配置步骤

### 1. 获取Coze凭证

1. 访问 [Coze平台](https://www.coze.cn/)
2. 注册账号并登录
3. 创建一个新的Bot
4. 获取以下信息：
   - Bot ID
   - Access Token

### 2. 配置项目

编辑 `components/coze_chat/coze_config.h` 文件：

```c
// 将以下占位符替换为您的实际值
#define COZE_BOT_ID_CONFIG "your_actual_bot_id"
#define COZE_ACCESS_TOKEN_CONFIG "your_actual_access_token"
```

### 3. 编译和烧录

```bash
idf.py build
idf.py flash monitor
```

## 使用方法

### 按键控制

- **按键1单击**: 开始/停止Coze录音对话
  - 第一次单击：开始录音，向Coze发送语音
  - 第二次单击：停止录音，等待Coze回复
- **按键1长按**: 设备开机/关机
- **按键2单击**: 切换GIF动画
- **按键2长按**: 进入WiFi配网模式

### 操作流程

1. 确保设备已连接WiFi（通过配网或之前保存的凭证）
2. 等待Coze聊天初始化完成（查看串口日志）
3. 单击按键1开始录音
4. 说话（录音中）
5. 再次单击按键1停止录音
6. 等待Coze处理并返回语音回复
7. 设备播放Coze的语音回复

### 状态指示

通过串口监控可以看到以下状态：

- `Coze chat initialized successfully`: Coze聊天初始化成功
- `Starting Coze recording...`: 开始录音
- `Stopping Coze recording...`: 停止录音
- `Received audio data: X bytes`: 接收到Coze返回的音频数据
- `Chat session completed`: 对话会话完成

## 技术实现

### 组件结构

```
components/coze_chat/
├── coze_chat.h          # 头文件
├── coze_chat.c          # 实现文件
├── coze_config.h        # 配置文件
└── CMakeLists.txt       # 构建配置
```

### 主要功能

1. **coze_chat_init()**: 初始化Coze聊天功能
2. **coze_chat_start_recording()**: 开始录音
3. **coze_chat_stop_recording()**: 停止录音
4. **coze_chat_cancel()**: 取消当前对话
5. **coze_chat_get_state()**: 获取当前状态

### 音频配置

- 采样率: 24000 Hz（与项目音频BSP一致）
- 位深度: 16 bit
- 声道: 单声道
- 格式: PCM

## 注意事项

### 网络要求

- 需要稳定的WiFi连接
- 确保网络可以访问Coze平台API
- 建议网络延迟低于100ms以获得最佳体验

### 音频要求

- 录音环境尽量安静
- 说话清晰，距离麦克风适中
- 避免长时间录音（建议每次录音不超过30秒）

### 错误处理

如果遇到问题，请检查：

1. WiFi连接状态
2. Coze凭证是否正确
3. 网络连接是否正常
4. 串口日志中的错误信息

## 开发扩展

### 自定义音频处理

如需自定义音频录音和播放逻辑，请修改：

- `coze_audio_recording_task()`: 录音任务
- `coze_audio_data_callback()`: 音频数据回调

### 添加UI反馈

可以在以下事件中添加UI反馈：

- 录音开始/停止
- 处理中状态
- 播放状态
- 错误状态

### 集成其他功能

Coze聊天组件设计为独立模块，可以轻松集成到其他功能中：

- 语音唤醒
- 多轮对话
- 自定义指令处理

## 故障排除

### 常见问题

1. **Coze聊天初始化失败**
   - 检查WiFi连接
   - 验证Bot ID和Access Token
   - 查看网络连接状态

2. **录音无响应**
   - 检查音频BSP初始化
   - 验证麦克风硬件连接
   - 查看录音任务状态

3. **无法接收回复**
   - 检查网络连接稳定性
   - 验证Coze平台状态
   - 查看WebSocket连接状态

### 调试建议

1. 启用详细日志输出
2. 监控网络连接状态
3. 检查音频数据流
4. 验证Coze平台响应

## 更新日志

- v1.0.0: 初始版本，基本对话功能
- 支持按键控制录音
- 自动WiFi连接后初始化
- 基础错误处理和状态监控
