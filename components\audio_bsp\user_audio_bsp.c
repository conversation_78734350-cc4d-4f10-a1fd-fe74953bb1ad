#include <stdio.h>
#include <string.h>
#include <math.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "user_audio_bsp.h"
#include "esp_log.h"
#include "esp_err.h"



#include "codec_board.h"
#include "codec_init.h"

#define SAMPLE_RATE     24000           // 采样率：24000Hz
#define BIT_DEPTH       32              // 位深：32位


esp_codec_dev_handle_t playback = NULL;
esp_codec_dev_handle_t record = NULL;


extern const uint8_t music_pcm_start[] asm("_binary_canon_pcm_start");
extern const uint8_t music_pcm_end[]   asm("_binary_canon_pcm_end");


void user_audio_bsp_init(void)
{
  set_codec_board_type("C6_AMOLED_1_43");
  codec_init_cfg_t codec_dev;
  init_codec(&codec_dev);
  playback = get_playback_handle();
  record = get_record_handle();
}


void i2s_music(void *args)
{
  esp_codec_dev_set_out_vol(playback, 60.0);  //设置60声音大小
  for(;;)
  {
    size_t bytes_write = 0;
    size_t bytes_sizt = music_pcm_end - music_pcm_start;
    uint8_t *data_ptr = (uint8_t *)music_pcm_start;
    esp_codec_dev_sample_info_t fs = {
      .sample_rate = 24000,
      .channel = 2,
      .bits_per_sample = 16,
    };
    if(esp_codec_dev_open(playback, &fs) == ESP_CODEC_DEV_OK)
    {
      while (bytes_write < bytes_sizt)
      {
        esp_codec_dev_write(playback, data_ptr, 256);
        data_ptr += 256;
        bytes_write += 256;
      }
      //esp_codec_dev_close(playback); //close 
    }
    else
    {
      break;
    }
  }
  vTaskDelete(NULL);
}

void i2s_echo(void *arg)
{
  esp_codec_dev_set_out_vol(playback, 90.0); //设置90声音大小
  esp_codec_dev_set_in_gain(record, 35.0);   //设置录音时的增益
  uint8_t *data_ptr = (uint8_t *)heap_caps_malloc(1024 * sizeof(uint8_t), MALLOC_CAP_DEFAULT);
  esp_codec_dev_sample_info_t fs = {
    .sample_rate = SAMPLE_RATE,
    .channel = 2,
    .bits_per_sample = BIT_DEPTH,
  };
  esp_codec_dev_open(playback, &fs); //打开播放
  esp_codec_dev_open(record, &fs);   //打开录音
  for(;;)
  {
    if(ESP_CODEC_DEV_OK == esp_codec_dev_read(record, data_ptr, 1024))
    {
      esp_codec_dev_write(playback, data_ptr, 1024);
    }
  }
}


void audio_playback_set_vol(uint8_t vol)
{
  esp_codec_dev_set_out_vol(playback, vol);   //设置60声音大小
}

// 音频播放状态管理
static audio_playback_state_t g_playback_state = AUDIO_PLAYBACK_STATE_IDLE;
static bool g_playback_device_opened = false;
static audio_format_t g_current_format = {0};

// 音频播放初始化
esp_err_t audio_playback_init(void)
{
    if (playback == NULL) {
        ESP_LOGE("AUDIO_BSP", "Playback device not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    g_playback_state = AUDIO_PLAYBACK_STATE_IDLE;
    g_playback_device_opened = false;

    ESP_LOGI("AUDIO_BSP", "Audio playback initialized");
    return ESP_OK;
}

// 音频播放去初始化
esp_err_t audio_playback_deinit(void)
{
    if (g_playback_device_opened && playback) {
        esp_codec_dev_close(playback);
        g_playback_device_opened = false;
    }

    g_playback_state = AUDIO_PLAYBACK_STATE_IDLE;
    ESP_LOGI("AUDIO_BSP", "Audio playback deinitialized");
    return ESP_OK;
}

// 开始音频播放
esp_err_t audio_playback_start(const audio_format_t *format)
{
    if (playback == NULL || format == NULL) {
        ESP_LOGE("AUDIO_BSP", "Invalid parameters for playback start");
        return ESP_ERR_INVALID_ARG;
    }

    // 如果设备已经打开且格式相同，直接返回成功
    if (g_playback_device_opened &&
        g_current_format.sample_rate == format->sample_rate &&
        g_current_format.channels == format->channels &&
        g_current_format.bits_per_sample == format->bits_per_sample) {
        g_playback_state = AUDIO_PLAYBACK_STATE_PLAYING;
        return ESP_OK;
    }

    // 如果设备已打开但格式不同，先关闭
    if (g_playback_device_opened) {
        esp_codec_dev_close(playback);
        g_playback_device_opened = false;
    }

    // 配置播放参数
    esp_codec_dev_sample_info_t playback_fs = {
        .sample_rate = format->sample_rate,
        .channel = format->channels,
        .bits_per_sample = format->bits_per_sample,
    };

    esp_err_t ret = esp_codec_dev_open(playback, &playback_fs);
    if (ret == ESP_CODEC_DEV_OK) {
        g_playback_device_opened = true;
        g_current_format = *format;
        g_playback_state = AUDIO_PLAYBACK_STATE_PLAYING;

        ESP_LOGI("AUDIO_BSP", "Playback started: %dHz, %dch, %dbit",
                 format->sample_rate, format->channels, format->bits_per_sample);
        return ESP_OK;
    } else {
        g_playback_state = AUDIO_PLAYBACK_STATE_ERROR;
        ESP_LOGE("AUDIO_BSP", "Failed to open playback device: %d", ret);
        return ESP_FAIL;
    }
}
















// 停止音频播放
esp_err_t audio_playback_stop(void)
{
    if (g_playback_device_opened && playback) {
        esp_codec_dev_close(playback);
        g_playback_device_opened = false;
        ESP_LOGI("AUDIO_BSP", "Playback stopped");
    }

    g_playback_state = AUDIO_PLAYBACK_STATE_IDLE;
    return ESP_OK;
}

// 写入音频数据
esp_err_t audio_playback_write_data(const uint8_t *data, size_t len)
{
    if (data == NULL || len == 0) {
        ESP_LOGW("AUDIO_BSP", "Invalid audio data");
        return ESP_ERR_INVALID_ARG;
    }

    if (!g_playback_device_opened || playback == NULL) {
        ESP_LOGW("AUDIO_BSP", "Playback device not opened");
        return ESP_ERR_INVALID_STATE;
    }

    if (g_playback_state != AUDIO_PLAYBACK_STATE_PLAYING) {
        ESP_LOGW("AUDIO_BSP", "Playback not in playing state");
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = esp_codec_dev_write(playback, data, len);
    if (ret == ESP_CODEC_DEV_OK) {
        ESP_LOGD("AUDIO_BSP", "Audio data written: %d bytes", len);
        return ESP_OK;
    } else {
        ESP_LOGE("AUDIO_BSP", "Failed to write audio data: %d", ret);
        g_playback_state = AUDIO_PLAYBACK_STATE_ERROR;
        return ESP_FAIL;
    }
}

// 设置播放音量
esp_err_t audio_playback_set_volume(float volume)
{
    if (playback == NULL) {
        ESP_LOGE("AUDIO_BSP", "Playback device not available");
        return ESP_ERR_INVALID_STATE;
    }

    // 限制音量范围 0.0 - 100.0
    if (volume < 0.0f) volume = 0.0f;
    if (volume > 100.0f) volume = 100.0f;

    esp_err_t ret = esp_codec_dev_set_out_vol(playback, volume);
    if (ret == ESP_CODEC_DEV_OK) {
        ESP_LOGI("AUDIO_BSP", "Volume set to %.1f", volume);
        return ESP_OK;
    } else {
        ESP_LOGE("AUDIO_BSP", "Failed to set volume: %d", ret);
        return ESP_FAIL;
    }
}

// 获取播放状态
audio_playback_state_t audio_playback_get_state(void)
{
    return g_playback_state;
}

// 获取播放设备句柄
esp_codec_dev_handle_t audio_get_playback_handle(void)
{
    return playback;
}

// 获取录音设备句柄
esp_codec_dev_handle_t audio_get_record_handle(void)
{
    return record;
}

// 测试音频播放功能 - 生成简单的正弦波测试音
esp_err_t audio_playback_test_tone(uint32_t frequency, uint32_t duration_ms)
{
    if (frequency == 0 || duration_ms == 0) {
        ESP_LOGE("AUDIO_BSP", "Invalid test parameters");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI("AUDIO_BSP", "Playing test tone: %dHz for %dms", frequency, duration_ms);

    // 配置测试音频格式
    audio_format_t test_format = {
        .sample_rate = 16000,
        .channels = 1,
        .bits_per_sample = 16,
    };

    // 启动播放
    esp_err_t ret = audio_playback_start(&test_format);
    if (ret != ESP_OK) {
        ESP_LOGE("AUDIO_BSP", "Failed to start playback for test");
        return ret;
    }

    // 设置测试音量
    audio_playback_set_volume(50.0);

    // 生成简单的正弦波数据
    const size_t buffer_size = 256;
    int16_t *audio_buffer = malloc(buffer_size * sizeof(int16_t));
    if (!audio_buffer) {
        ESP_LOGE("AUDIO_BSP", "Failed to allocate test audio buffer");
        audio_playback_stop();
        return ESP_ERR_NO_MEM;
    }

    // 计算总样本数
    uint32_t total_samples = (test_format.sample_rate * duration_ms) / 1000;
    uint32_t samples_written = 0;

    // 生成并播放音频数据
    while (samples_written < total_samples) {
        size_t samples_to_write = (total_samples - samples_written > buffer_size) ?
                                  buffer_size : (total_samples - samples_written);

        // 生成正弦波数据
        for (size_t i = 0; i < samples_to_write; i++) {
            float time = (float)(samples_written + i) / test_format.sample_rate;
            float amplitude = 0.3f; // 30% 音量
            audio_buffer[i] = (int16_t)(amplitude * 32767.0f * sinf(2.0f * M_PI * frequency * time));
        }

        // 写入音频数据
        ret = audio_playback_write_data((uint8_t*)audio_buffer, samples_to_write * sizeof(int16_t));
        if (ret != ESP_OK) {
            ESP_LOGE("AUDIO_BSP", "Failed to write test audio data");
            break;
        }

        samples_written += samples_to_write;

        // 短暂延时以避免缓冲区溢出
        vTaskDelay(pdMS_TO_TICKS(10));
    }

    free(audio_buffer);

    // 等待播放完成
    vTaskDelay(pdMS_TO_TICKS(100));

    // 停止播放
    audio_playback_stop();

    ESP_LOGI("AUDIO_BSP", "Test tone playback completed");
    return ESP_OK;
}

/*
Board: AMOLED_1_43
i2c: {sda: 18, scl: 8}
i2s: {bclk: 21, ws: 22, dout: 23, din: 20, mclk: 19}
out: {codec: ES8311, pa: -1, use_mclk: 1, pa_gain:6}
in: {codec: ES7210}
*/