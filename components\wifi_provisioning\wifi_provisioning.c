#include "wifi_provisioning.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include <string.h>

static const char *TAG = "wifi_prov";

// WiFi事件位
#define WIFI_CONNECTED_BIT    BIT0
#define WIFI_FAIL_BIT         BIT1

// 静态变量
static esp_netif_t *s_sta_netif = NULL;
static EventGroupHandle_t s_wifi_event_group = NULL;
static wifi_prov_callback_t s_callback = NULL;
static wifi_prov_state_t s_state = WIFI_PROV_STATE_IDLE;
static int s_retry_num = 0;
static const int WIFI_MAXIMUM_RETRY = 5;

// 当前连接信息
static wifi_ap_record_t s_current_ap_info = {0};
static esp_netif_ip_info_t s_current_ip_info = {0};

/**
 * @brief WiFi事件处理函数
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (s_retry_num < WIFI_MAXIMUM_RETRY) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "retry to connect to the AP");
        } else {
            xEventGroupSetBits(s_wifi_event_group, WIFI_FAIL_BIT);
            s_state = WIFI_PROV_STATE_FAILED;
            if (s_callback) {
                s_callback(s_state, NULL);
            }
        }
        ESP_LOGI(TAG,"connect to the AP fail");
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "got ip:" IPSTR, IP2STR(&event->ip_info.ip));
        s_retry_num = 0;
        
        // 保存IP信息
        memcpy(&s_current_ip_info, &event->ip_info, sizeof(esp_netif_ip_info_t));
        
        // 获取AP信息
        esp_wifi_sta_get_ap_info(&s_current_ap_info);
        
        s_state = WIFI_PROV_STATE_CONNECTED;
        xEventGroupSetBits(s_wifi_event_group, WIFI_CONNECTED_BIT);
        
        if (s_callback) {
            s_callback(s_state, &s_current_ip_info);
        }
    }
}

esp_err_t wifi_prov_init(wifi_prov_callback_t callback)
{
    esp_err_t ret = ESP_OK;
    
    if (s_sta_netif != NULL) {
        ESP_LOGW(TAG, "WiFi provisioning already initialized");
        return ESP_OK;
    }
    
    s_callback = callback;
    s_state = WIFI_PROV_STATE_IDLE;
    
    // 创建事件组
    s_wifi_event_group = xEventGroupCreate();
    if (s_wifi_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_ERR_NO_MEM;
    }
    
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    
    // 创建默认事件循环
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // 创建默认WiFi STA
    s_sta_netif = esp_netif_create_default_wifi_sta();
    
    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    // 注册事件处理器
    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_any_id));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        &instance_got_ip));
    
    // 设置WiFi模式
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    
    // 启动WiFi
    ESP_ERROR_CHECK(esp_wifi_start());
    
    ESP_LOGI(TAG, "WiFi provisioning initialized");
    return ret;
}

esp_err_t wifi_prov_deinit(void)
{
    if (s_sta_netif == NULL) {
        return ESP_OK;
    }
    
    // 停止WiFi
    esp_wifi_stop();
    esp_wifi_deinit();
    
    // 销毁网络接口
    esp_netif_destroy_default_wifi(s_sta_netif);
    s_sta_netif = NULL;
    
    // 删除事件组
    if (s_wifi_event_group) {
        vEventGroupDelete(s_wifi_event_group);
        s_wifi_event_group = NULL;
    }
    
    s_callback = NULL;
    s_state = WIFI_PROV_STATE_IDLE;
    
    ESP_LOGI(TAG, "WiFi provisioning deinitialized");
    return ESP_OK;
}

esp_err_t wifi_prov_connect(const char *credentials, size_t len)
{
    if (credentials == NULL || len == 0) {
        ESP_LOGE(TAG, "Invalid credentials");
        return ESP_ERR_INVALID_ARG;
    }
    
    // 查找分隔符 '#'
    char *delimiter = strchr(credentials, '#');
    if (delimiter == NULL) {
        ESP_LOGE(TAG, "Invalid credentials format, expected 'ssid#password'");
        return ESP_ERR_INVALID_ARG;
    }
    
    // 计算SSID和密码长度
    size_t ssid_len = delimiter - credentials;
    size_t password_len = len - ssid_len - 1;
    
    if (ssid_len == 0 || ssid_len >= sizeof(((wifi_sta_config_t*)0)->ssid)) {
        ESP_LOGE(TAG, "Invalid SSID length: %d", ssid_len);
        return ESP_ERR_INVALID_ARG;
    }
    
    if (password_len >= sizeof(((wifi_sta_config_t*)0)->password)) {
        ESP_LOGE(TAG, "Invalid password length: %d", password_len);
        return ESP_ERR_INVALID_ARG;
    }
    
    // 配置WiFi
    wifi_config_t wifi_config = {0};
    
    // 复制SSID
    memcpy(wifi_config.sta.ssid, credentials, ssid_len);
    wifi_config.sta.ssid[ssid_len] = '\0';
    
    // 复制密码
    if (password_len > 0) {
        memcpy(wifi_config.sta.password, delimiter + 1, password_len);
        wifi_config.sta.password[password_len] = '\0';
    }
    
    // 设置其他参数
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;
    wifi_config.sta.pmf_cfg.capable = true;
    wifi_config.sta.pmf_cfg.required = false;
    
    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", wifi_config.sta.ssid);
    
    // 设置WiFi配置
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    
    // 重置重试计数器
    s_retry_num = 0;
    s_state = WIFI_PROV_STATE_CONNECTING;
    
    if (s_callback) {
        s_callback(s_state, NULL);
    }
    
    // 开始连接
    esp_err_t ret = esp_wifi_connect();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi connection: %s", esp_err_to_name(ret));
        s_state = WIFI_PROV_STATE_FAILED;
        if (s_callback) {
            s_callback(s_state, NULL);
        }
        return ret;
    }
    
    return ESP_OK;
}

esp_err_t wifi_prov_disconnect(void)
{
    esp_err_t ret = esp_wifi_disconnect();
    if (ret == ESP_OK) {
        s_state = WIFI_PROV_STATE_DISCONNECTED;
        if (s_callback) {
            s_callback(s_state, NULL);
        }
    }
    return ret;
}

wifi_prov_state_t wifi_prov_get_state(void)
{
    return s_state;
}

esp_err_t wifi_prov_get_info(wifi_ap_record_t *ap_info, esp_netif_ip_info_t *ip_info)
{
    if (s_state != WIFI_PROV_STATE_CONNECTED) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (ap_info) {
        memcpy(ap_info, &s_current_ap_info, sizeof(wifi_ap_record_t));
    }
    
    if (ip_info) {
        memcpy(ip_info, &s_current_ip_info, sizeof(esp_netif_ip_info_t));
    }
    
    return ESP_OK;
}
