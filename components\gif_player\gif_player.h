/**
 * @file gif_player.h
 * @brief ESP-IDF component for playing animated GIFs using LVGL
 * 
 * This component provides a reusable interface for displaying animated GIF files
 * using LVGL's GIF decoder library. It supports continuous looping and proper
 * resource management.
 */

#ifndef GIF_PLAYER_H
#define GIF_PLAYER_H

#include "lvgl.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief GIF player error codes
 */
typedef enum {
    GIF_PLAYER_OK = 0,              /*!< Success */
    GIF_PLAYER_ERR_INVALID_ARG,     /*!< Invalid argument */
    GIF_PLAYER_ERR_FILE_NOT_FOUND,  /*!< GIF file not found */
    GIF_PLAYER_ERR_MEMORY,          /*!< Memory allocation failed */
    GIF_PLAYER_ERR_LVGL_NOT_INIT,   /*!< LVGL not initialized */
    GIF_PLAYER_ERR_GIF_DECODE       /*!< GIF decoding error */
} gif_player_err_t;

/**
 * @brief GIF player data source type
 */
typedef enum {
    GIF_PLAYER_SRC_EMBEDDED,       /*!< Use embedded data array */
    GIF_PLAYER_SRC_MMAP            /*!< Use memory-mapped assets */
} gif_player_src_type_t;

/**
 * @brief GIF player configuration structure
 */
typedef struct {
    gif_player_src_type_t src_type; /*!< Data source type */
    union {
        struct {
            const uint8_t *gif_data;   /*!< Pointer to GIF data array */
            size_t gif_data_size;      /*!< Size of GIF data */
        } embedded;                    /*!< Embedded data configuration */
        struct {
            int asset_index;           /*!< Asset index in mmap */
        } mmap;                        /*!< Memory-mapped asset configuration */
    } src;                             /*!< Data source configuration */
    lv_obj_t *parent;              /*!< Parent object for the GIF widget */
    lv_coord_t x;                  /*!< X position */
    lv_coord_t y;                  /*!< Y position */
    uint16_t zoom;                 /*!< Zoom factor (256 = 100%, 512 = 200%, etc.) */
    bool auto_start;               /*!< Auto start playback */
    bool loop_forever;             /*!< Loop the GIF forever */
} gif_player_config_t;

/**
 * @brief GIF player handle
 */
typedef struct gif_player_s *gif_player_handle_t;

/**
 * @brief Initialize the GIF player component
 *
 * This function must be called before using any other GIF player functions.
 *
 * @return
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_LVGL_NOT_INIT: LVGL not initialized
 */
gif_player_err_t gif_player_init(void);

/**
 * @brief Create a new GIF player instance
 *
 * @param config Pointer to the configuration structure
 * @param handle Pointer to store the created handle
 * @return
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid arguments
 *     - GIF_PLAYER_ERR_MEMORY: Memory allocation failed
 *     - GIF_PLAYER_ERR_FILE_NOT_FOUND: GIF file not found
 *     - GIF_PLAYER_ERR_GIF_DECODE: GIF decoding error
 */
gif_player_err_t gif_player_create(const gif_player_config_t *config, gif_player_handle_t *handle);

/**
 * @brief Create a new GIF player instance from memory-mapped asset
 *
 * @param asset_index Index of the asset in memory-mapped partition
 * @param parent Parent object for the GIF widget
 * @param x X position
 * @param y Y position
 * @param handle Pointer to store the created handle
 * @return
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid arguments
 *     - GIF_PLAYER_ERR_MEMORY: Memory allocation failed
 */
gif_player_err_t gif_player_create_from_mmap(int asset_index, lv_obj_t *parent,
                                              lv_coord_t x, lv_coord_t y, uint16_t zoom,bool loop_forever,
                                              gif_player_handle_t *handle);

/**
 * @brief Start GIF playback
 * 
 * @param handle GIF player handle
 * @return 
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_start(gif_player_handle_t handle);

/**
 * @brief Stop GIF playback
 * 
 * @param handle GIF player handle
 * @return 
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_stop(gif_player_handle_t handle);

/**
 * @brief Restart GIF playback from the beginning
 * 
 * @param handle GIF player handle
 * @return 
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_restart(gif_player_handle_t handle);

/**
 * @brief Set the position of the GIF widget
 *
 * @param handle GIF player handle
 * @param x X coordinate
 * @param y Y coordinate
 * @return
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_set_pos(gif_player_handle_t handle, lv_coord_t x, lv_coord_t y);

/**
 * @brief Set the zoom factor of the GIF widget
 *
 * @param handle GIF player handle
 * @param zoom Zoom factor (256 = 100%, 512 = 200%, 128 = 50%, etc.)
 * @return
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_set_zoom(gif_player_handle_t handle, uint16_t zoom);

/**
 * @brief Get the LVGL object associated with the GIF player
 * 
 * @param handle GIF player handle
 * @return LVGL object pointer or NULL if invalid handle
 */
lv_obj_t *gif_player_get_obj(gif_player_handle_t handle);

/**
 * @brief Destroy a GIF player instance and free resources
 * 
 * @param handle GIF player handle
 * @return 
 *     - GIF_PLAYER_OK: Success
 *     - GIF_PLAYER_ERR_INVALID_ARG: Invalid handle
 */
gif_player_err_t gif_player_destroy(gif_player_handle_t handle);

#ifdef __cplusplus
}
#endif

#endif /* GIF_PLAYER_H */
