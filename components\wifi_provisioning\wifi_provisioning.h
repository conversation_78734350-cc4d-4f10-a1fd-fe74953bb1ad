#ifndef WIFI_PROVISIONING_H
#define WIFI_PROVISIONING_H

#include "esp_err.h"
#include "esp_wifi.h"
#include "esp_event.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief WiFi配网状态
 */
typedef enum {
    WIFI_PROV_STATE_IDLE,           ///< 空闲状态
    WIFI_PROV_STATE_CONNECTING,     ///< 正在连接
    WIFI_PROV_STATE_CONNECTED,      ///< 已连接
    WIFI_PROV_STATE_FAILED,         ///< 连接失败
    WIFI_PROV_STATE_DISCONNECTED    ///< 已断开连接
} wifi_prov_state_t;

/**
 * @brief WiFi配网回调函数类型
 * 
 * @param state WiFi配网状态
 * @param ip_info IP信息（仅在连接成功时有效）
 */
typedef void (*wifi_prov_callback_t)(wifi_prov_state_t state, esp_netif_ip_info_t *ip_info);

/**
 * @brief 初始化WiFi配网组件
 * 
 * @param callback 状态回调函数
 * @return esp_err_t 
 */
esp_err_t wifi_prov_init(wifi_prov_callback_t callback);

/**
 * @brief 反初始化WiFi配网组件
 * 
 * @return esp_err_t 
 */
esp_err_t wifi_prov_deinit(void);

/**
 * @brief 解析并连接WiFi
 * 
 * @param credentials WiFi凭据字符串，格式为 "ssid#password"
 * @param len 凭据字符串长度
 * @return esp_err_t 
 */
esp_err_t wifi_prov_connect(const char *credentials, size_t len);

/**
 * @brief 断开WiFi连接
 * 
 * @return esp_err_t 
 */
esp_err_t wifi_prov_disconnect(void);

/**
 * @brief 获取当前WiFi配网状态
 * 
 * @return wifi_prov_state_t 
 */
wifi_prov_state_t wifi_prov_get_state(void);

/**
 * @brief 获取当前连接的WiFi信息
 * 
 * @param ap_info AP信息
 * @param ip_info IP信息
 * @return esp_err_t 
 */
esp_err_t wifi_prov_get_info(wifi_ap_record_t *ap_info, esp_netif_ip_info_t *ip_info);

#ifdef __cplusplus
}
#endif

#endif // WIFI_PROVISIONING_H
