# For more information about build system see
# https://docs.espressif.com/projects/esp-idf/en/latest/api-guides/build-system.html
# The following five lines of boilerplate have to be in your project's
# CMakeLists in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(09_LVGL_V8_Test)

# Configure esp_mmap_assets for GIF files
spiffs_create_partition_assets(
    gif_assets
    assets
    FLASH_IN_PROJECT
    MMAP_FILE_SUPPORT_FORMAT ".gif,.mp3"
)
